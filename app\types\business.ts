export interface TaskResult {
  question: string
  answer: string | number
  calculation?: string
}

export interface Task {
  title: string
  description: string
  results: TaskResult[]
}

export interface BusinessAnalysis {
  analystId: number
  tasks: Task[]
}

export interface FunctionValues {
  F: (x: number) => number
  V: (x: number) => number
  C: (x: number) => number
  R: (x: number) => number
  P: (x: number) => number
  p: (x: number) => number
  p2: (x: number) => number
  R2: (x: number) => number
  AP: (x: number) => number
  B: (r: number) => number
  T: (b: number) => number
  N: (x: number) => number
}
