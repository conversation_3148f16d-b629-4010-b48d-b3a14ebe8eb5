<template>
  <UCard>
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-bold text-gray-900 dark:text-white">
            📋 Answer Key for Analyst ID:
            <span class="text-2xl font-mono bg-blue-100 dark:bg-blue-900 px-2 py-1 rounded">{{ analystId }}</span>
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
            ✅ Use these reference solutions to grade student work accurately
          </p>
        </div>
        <UBadge color="green" variant="solid" size="lg">
          📖 Reference Solutions
        </UBadge>
      </div>
    </template>

    <div class="space-y-8">
      <!-- Task 1: Cost Analysis -->
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3">1</div>
          <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200">
            📊 Task 1: Cost Analysis (Addition)
          </h3>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-400">
              <div class="text-xs text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">Question 1</div>
              <div class="font-semibold text-gray-900 dark:text-white">Total cost function:</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                C(x) = {{ task1.results[0].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-400">
              <div class="text-xs text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">Question 2</div>
              <div class="font-semibold text-gray-900 dark:text-white">Cost of 10 units:</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task1.results[1].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-400">
              <div class="text-xs text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">Question 3</div>
              <div class="font-semibold text-gray-900 dark:text-white">Cost of 50 units:</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task1.results[2].answer }}
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-400">
              <div class="text-xs text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">Question 4</div>
              <div class="font-semibold text-gray-900 dark:text-white">Additional cost (10→50):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task1.results[3].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-blue-400">
              <div class="text-xs text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">Question 5</div>
              <div class="font-semibold text-gray-900 dark:text-white">Cost if A doubled:</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task1.results[4].answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Task 2: Profit Analysis -->
      <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-3">2</div>
          <h3 class="text-lg font-bold text-green-800 dark:text-green-200">
            💰 Task 2: Profit Analysis (Subtraction)
          </h3>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-400">
              <div class="text-xs text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">Question 1</div>
              <div class="font-semibold text-gray-900 dark:text-white">Profit function:</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                P(x) = {{ task2.results[0].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-400">
              <div class="text-xs text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">Question 2</div>
              <div class="font-semibold text-gray-900 dark:text-white">Profit (10 units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task2.results[1].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-400">
              <div class="text-xs text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">Question 3</div>
              <div class="font-semibold text-gray-900 dark:text-white">Profit (100 units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task2.results[2].answer }}
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-400">
              <div class="text-xs text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">Question 4</div>
              <div class="font-semibold text-gray-900 dark:text-white">Marginal profit:</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task2.results[3].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-green-400">
              <div class="text-xs text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">Question 5</div>
              <div class="font-semibold text-gray-900 dark:text-white">Profit (1 unit):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task2.results[4].answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Task 3: Unit Revenue Analysis -->
      <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-3">3</div>
          <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200">
            📈 Task 3: Unit Revenue Analysis (Multiplication)
          </h3>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-400">
              <div class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">Question 1</div>
              <div class="font-semibold text-gray-900 dark:text-white">R(x) verification:</div>
              <div class="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                {{ task3.results[0].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-400">
              <div class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">Question 2</div>
              <div class="font-semibold text-gray-900 dark:text-white">Sister product R₂(x):</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                {{ task3.results[1].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-400">
              <div class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">Question 3</div>
              <div class="font-semibold text-gray-900 dark:text-white">Revenue (100 sister units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task3.results[2].answer }}
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-400">
              <div class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">Question 4</div>
              <div class="font-semibold text-gray-900 dark:text-white">Revenue (200 sister units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task3.results[3].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-purple-400">
              <div class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">Question 5</div>
              <div class="font-semibold text-gray-900 dark:text-white">Price effect:</div>
              <div class="text-sm font-mono bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded mt-1">
                {{ task3.results[4].answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Task 4: Efficiency Analysis -->
      <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-bold mr-3">4</div>
          <h3 class="text-lg font-bold text-orange-800 dark:text-orange-200">
            ⚡ Task 4: Efficiency Analysis (Division)
          </h3>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-orange-400">
              <div class="text-xs text-orange-600 dark:text-orange-400 font-medium uppercase tracking-wide">Question 1</div>
              <div class="font-semibold text-gray-900 dark:text-white">Average profit function:</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                AP(x) = {{ task4.results[0].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-orange-400">
              <div class="text-xs text-orange-600 dark:text-orange-400 font-medium uppercase tracking-wide">Question 2</div>
              <div class="font-semibold text-gray-900 dark:text-white">AP (20 units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task4.results[1].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-orange-400">
              <div class="text-xs text-orange-600 dark:text-orange-400 font-medium uppercase tracking-wide">Question 3</div>
              <div class="font-semibold text-gray-900 dark:text-white">AP (50 units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task4.results[2].answer }}
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-orange-400">
              <div class="text-xs text-orange-600 dark:text-orange-400 font-medium uppercase tracking-wide">Question 4</div>
              <div class="font-semibold text-gray-900 dark:text-white">AP (100 units):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task4.results[3].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-orange-400">
              <div class="text-xs text-orange-600 dark:text-orange-400 font-medium uppercase tracking-wide">Question 5</div>
              <div class="font-semibold text-gray-900 dark:text-white">Limit as x→∞:</div>
              <div class="text-lg font-mono bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mt-1">
                {{ task4.results[4].answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Task 5: Net Bonus Analysis -->
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold mr-3">5</div>
          <h3 class="text-lg font-bold text-red-800 dark:text-red-200">
            🎯 Task 5: Net Bonus Analysis (Composition)
          </h3>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-red-400">
              <div class="text-xs text-red-600 dark:text-red-400 font-medium uppercase tracking-wide">Question 1</div>
              <div class="font-semibold text-gray-900 dark:text-white">Net bonus function:</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                {{ task5.results[0].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-red-400">
              <div class="text-xs text-red-600 dark:text-red-400 font-medium uppercase tracking-wide">Question 2</div>
              <div class="font-semibold text-gray-900 dark:text-white">Bonus (₱20,000 revenue):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task5.results[1].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-red-400">
              <div class="text-xs text-red-600 dark:text-red-400 font-medium uppercase tracking-wide">Question 3</div>
              <div class="font-semibold text-gray-900 dark:text-white">Bonus (₱50,000 revenue):</div>
              <div class="text-lg font-mono bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded mt-1">
                {{ task5.results[2].answer }}
              </div>
            </div>
          </div>

          <div class="space-y-3">
            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-red-400">
              <div class="text-xs text-red-600 dark:text-red-400 font-medium uppercase tracking-wide">Question 4</div>
              <div class="font-semibold text-gray-900 dark:text-white">Intermediate B(R(x)):</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                {{ task5.results[3].answer }}
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-3 rounded border-l-4 border-red-400">
              <div class="text-xs text-red-600 dark:text-red-400 font-medium uppercase tracking-wide">Question 5</div>
              <div class="font-semibold text-gray-900 dark:text-white">New composite N₂(x):</div>
              <div class="text-lg font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
                {{ task5.results[4].answer }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Reference -->
    <template #footer>
      <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h4 class="text-sm font-bold text-gray-900 dark:text-white mb-3 flex items-center">
          🔍 Quick Reference Formulas for A = {{ analystId }}
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-xs">
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-blue-600 dark:text-blue-400">Fixed Cost:</div>
            <div class="font-mono">F(x) = 100x + {{ 50 * analystId }}</div>
          </div>
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-blue-600 dark:text-blue-400">Variable Cost:</div>
            <div class="font-mono">V(x) = 2x² + {{ 2 * analystId }}x</div>
          </div>
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-green-600 dark:text-green-400">Total Cost:</div>
            <div class="font-mono">C(x) = 2x² + {{ 100 + 2 * analystId }}x + {{ 50 * analystId }}</div>
          </div>
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-purple-600 dark:text-purple-400">Revenue:</div>
            <div class="font-mono">R(x) = -3x² + 1000x</div>
          </div>
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-green-600 dark:text-green-400">Profit:</div>
            <div class="font-mono">P(x) = -5x² + {{ 900 - 2 * analystId }}x - {{ 50 * analystId }}</div>
          </div>
          <div class="bg-white dark:bg-gray-700 p-2 rounded border">
            <div class="font-medium text-orange-600 dark:text-orange-400">Average Profit:</div>
            <div class="font-mono">AP(x) = P(x)/x</div>
          </div>
        </div>
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
interface Props {
  analystId: number
}

const props = defineProps<Props>()

// Use the business analysis composable to get calculated answers
const { task1, task2, task3, task4, task5, analystId: businessAnalystId } = useBusinessAnalysis()

// Set the analyst ID to get the correct calculations
businessAnalystId.value = props.analystId

// Watch for changes in props.analystId to ensure reactivity
watch(() => props.analystId, (newId) => {
  businessAnalystId.value = newId
}, { immediate: true })
</script>
