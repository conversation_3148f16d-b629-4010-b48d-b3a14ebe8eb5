<template>
  <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
    <!-- Crite<PERSON> Header -->
    <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <h4 class="font-semibold text-gray-900 dark:text-white">
            {{ criterion.name }}
          </h4>
          <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
            {{ criterion.description }}
          </p>
        </div>
        <div class="ml-4 text-right">
          <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
            {{ currentScore }}/{{ criterion.maxPoints }}
          </div>
          <div class="text-xs text-gray-500">
            {{ Math.round((currentScore / criterion.maxPoints) * 100) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- Score Selection Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-100 dark:bg-gray-700">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16">
              Select
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
              Points
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Performance Level & Description
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          <tr
            v-for="level in sortedLevels"
            :key="level.points"
            class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
            :class="{
              'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500': currentScore === level.points,
              'hover:bg-blue-25 dark:hover:bg-blue-900/10': currentScore !== level.points
            }"
            @click="selectScore(level.points)"
          >
            <td class="px-4 py-3">
              <input
                type="radio"
                :value="level.points"
                :checked="currentScore === level.points"
                @change="selectScore(level.points)"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 cursor-pointer"
              />
            </td>
            <td class="px-4 py-3">
              <span class="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                {{ level.pointRange }}
              </span>
            </td>
            <td class="px-4 py-3">
              <span class="text-gray-700 dark:text-gray-300 text-sm">
                {{ level.description }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RubricCriterion } from '~/types/grading'

interface Props {
  criterion: RubricCriterion
  currentScore: number
}

interface Emits {
  updateScore: [criterionId: string, score: number]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Sort levels by points descending for display
const sortedLevels = computed(() => {
  return [...props.criterion.levels].sort((a, b) => b.points - a.points)
})

// Select score function
const selectScore = (score: number) => {
  emit('updateScore', props.criterion.id, score)
}
</script>
