<template>
  <div class="container mx-auto p-6">
    <h1 class="text-3xl font-bold mb-6">Test Flexible Point Scoring</h1>
    
    <div class="space-y-6">
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">Point Range Utilities Test</h2>
        </template>
        
        <div class="space-y-4">
          <div>
            <h3 class="font-medium mb-2">Range: "10-9"</h3>
            <p>Parsed: {{ JSON.stringify(parsePointRange("10-9")) }}</p>
            <p>Points in range: {{ getPointsInRange("10-9") }}</p>
            <p>Is 9 valid? {{ isValidPointForRange(9, "10-9") }}</p>
            <p>Is 8 valid? {{ isValidPointForRange(8, "10-9") }}</p>
          </div>
          
          <div>
            <h3 class="font-medium mb-2">Range: "8-7"</h3>
            <p>Parsed: {{ JSON.stringify(parsePointRange("8-7")) }}</p>
            <p>Points in range: {{ getPointsInRange("8-7") }}</p>
          </div>
          
          <div>
            <h3 class="font-medium mb-2">Range: "2-0"</h3>
            <p>Parsed: {{ JSON.stringify(parsePointRange("2-0")) }}</p>
            <p>Points in range: {{ getPointsInRange("2-0") }}</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">Sample Rubric Scoring</h2>
        </template>
        
        <RubricScoring
          :criterion="sampleCriterion"
          :current-score="currentScore"
          @update-score="updateScore"
        />
        
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded">
          <p><strong>Current Score:</strong> {{ currentScore }}/{{ sampleCriterion.maxPoints }}</p>
          <p><strong>Percentage:</strong> {{ Math.round((currentScore / sampleCriterion.maxPoints) * 100) }}%</p>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { parsePointRange, getPointsInRange, isValidPointForRange } from '~/types/grading'
import type { RubricCriterion } from '~/types/grading'

// Sample criterion for testing
const sampleCriterion: RubricCriterion = {
  id: 'test',
  name: 'Test Criterion',
  description: 'Testing flexible point assignment',
  maxPoints: 10,
  levels: [
    {
      pointRange: '10-9',
      points: 10,
      description: 'Excellent: Outstanding work with perfect understanding'
    },
    {
      pointRange: '8-7',
      points: 8,
      description: 'Good: Solid work with minor issues'
    },
    {
      pointRange: '6-5',
      points: 6,
      description: 'Satisfactory: Adequate work with some problems'
    },
    {
      pointRange: '4-3',
      points: 4,
      description: 'Needs Improvement: Below expectations'
    },
    {
      pointRange: '2-0',
      points: 2,
      description: 'Incomplete: Missing or severely inadequate'
    }
  ]
}

const currentScore = ref(0)

const updateScore = (criterionId: string, score: number) => {
  currentScore.value = score
  console.log(`Updated score for ${criterionId}: ${score}`)
}
</script>
