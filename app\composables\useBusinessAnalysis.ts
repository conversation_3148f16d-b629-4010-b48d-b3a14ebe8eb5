import type { BusinessAnalysis, FunctionValues, Task } from '~/types/business'

export const useBusinessAnalysis = () => {
  const analystId = ref<number>(0)

  // Define all mathematical functions based on analyst ID (A)
  const functions = computed<FunctionValues>(() => {
    const A = analystId.value

    return {
      // Task 1: Cost Analysis
      F: (x: number) => 100 * x + 50 * A, // Fixed costs
      V: (x: number) => 2 * x * x + 2 * A * x, // Variable costs
      C: (x: number) => 2 * x * x + (100 + 2 * A) * x + 50 * A, // Total cost

      // Task 2: Profit Analysis
      R: (x: number) => -3 * x * x + 1000 * x, // Revenue
      P: (x: number) => -5 * x * x + (900 - 2 * A) * x - 50 * A, // Profit

      // Task 3: Unit Revenue Analysis
      p: (x: number) => 1000 - 3 * x, // Price function
      p2: (x: number) => 500 - x, // Sister product price
      R2: (x: number) => 500 * x - x * x, // Sister product revenue

      // Task 4: Efficiency Analysis
      AP: (x: number) => (x === 0 ? 0 : (-5 * x * x + (900 - 2 * A) * x - 50 * A) / x), // Average profit

      // Task 5: Net Bonus Analysis
      B: (r: number) => 0.1 * r - 500, // Gross bonus
      T: (b: number) => 0.85 * b, // After tax
      N: (x: number) => 0.85 * (0.1 * (-3 * x * x + 1000 * x) - 500), // Net bonus composite
    }
  })

  // Format currency
  const formatCurrency = (value: number): string => {
    return `₱${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  // Task 1: Cost Analysis
  const task1 = computed<Task>(() => {
    const { F, V, C } = functions.value
    const A = analystId.value

    return {
      title: 'Task 1: Cost Analysis (Addition)',
      description: 'Analyzing fixed and variable costs to determine total production cost.',
      results: [
        {
          question: '1. Find the total cost function C(x)',
          answer: `C(x) = 2x² + ${100 + 2 * A}x + ${50 * A}`,
          calculation: `C(x) = F(x) + V(x) = (100x + ${50 * A}) + (2x² + ${2 * A}x) = 2x² + ${100 + 2 * A}x + ${50 * A}`,
        },
        {
          question: '2. Total cost of producing 10 units',
          answer: formatCurrency(C(10)),
          calculation: `C(10) = 2(10)² + ${100 + 2 * A}(10) + ${50 * A} = ${C(10)}`,
        },
        {
          question: '3. Total cost of producing 50 units',
          answer: formatCurrency(C(50)),
          calculation: `C(50) = 2(50)² + ${100 + 2 * A}(50) + ${50 * A} = ${C(50)}`,
        },
        {
          question: '4. Additional cost from 10 to 50 units',
          answer: formatCurrency(C(50) - C(10)),
          calculation: `C(50) - C(10) = ${C(50)} - ${C(10)} = ${C(50) - C(10)}`,
        },
        {
          question: '5. Total cost for 10 units if A were doubled',
          answer: formatCurrency(2 * (10 * 10) + (100 + 2 * (2 * A)) * 10 + 50 * (2 * A)),
          calculation: `If A = ${2 * A}, then C(10) = 2(10)² + ${100 + 4 * A}(10) + ${100 * A} = ${2 * (10 * 10) + (100 + 2 * (2 * A)) * 10 + 50 * (2 * A)}`,
        },
      ],
    }
  })

  // Task 2: Profit Analysis
  const task2 = computed<Task>(() => {
    const { R, C, P } = functions.value
    const A = analystId.value

    return {
      title: 'Task 2: Profit Analysis (Subtraction)',
      description: 'Calculating profit by subtracting costs from revenue.',
      results: [
        {
          question: '1. Find the profit function P(x)',
          answer: `P(x) = -5x² + ${900 - 2 * A}x - ${50 * A}`,
          calculation: `P(x) = R(x) - C(x) = (-3x² + 1000x) - (2x² + ${100 + 2 * A}x + ${50 * A}) = -3x² + 1000x - 2x² - ${100 + 2 * A}x - ${50 * A} = -5x² + ${900 - 2 * A}x - ${50 * A}`,
        },
        {
          question: '2. Profit when 10 units are sold',
          answer: formatCurrency(P(10)),
          calculation: `P(10) = -5(10)² + ${900 - 2 * A}(10) - ${50 * A} = ${P(10)}`,
        },
        {
          question: '3. Profit when 100 units are sold',
          answer: formatCurrency(P(100)),
          calculation: `P(100) = -5(100)² + ${900 - 2 * A}(100) - ${50 * A} = ${P(100)}`,
        },
        {
          question: '4. Marginal profit from 10 to 100 units',
          answer: formatCurrency(P(100) - P(10)),
          calculation: `P(100) - P(10) = ${P(100)} - ${P(10)} = ${P(100) - P(10)}`,
        },
        {
          question: '5. Profit for selling 1 unit',
          answer: formatCurrency(P(1)),
          calculation: `P(1) = -5(1)² + ${900 - 2 * A}(1) - ${50 * A} = ${P(1)}`,
        },
      ],
    }
  })

  // Task 3: Unit Revenue Analysis
  const task3 = computed<Task>(() => {
    const { R, p, p2, R2 } = functions.value

    return {
      title: 'Task 3: Unit Revenue Analysis (Multiplication)',
      description: 'Analyzing revenue through price and unit relationships.',
      results: [
        {
          question: '1. Confirm R(x) = x * p(x)',
          answer: 'Verified: x * (1000 - 3x) = 1000x - 3x² = R(x)',
          calculation: 'x * p(x) = x * (1000 - 3x) = 1000x - 3x² = -3x² + 1000x = R(x)',
        },
        {
          question: '2. Sister product revenue function R₂(x)',
          answer: 'R₂(x) = 500x - x²',
          calculation: 'R₂(x) = x * p₂(x) = x * (500 - x) = 500x - x²',
        },
        {
          question: '3. Revenue from 100 units of sister product',
          answer: formatCurrency(R2(100)),
          calculation: `R₂(100) = 500(100) - (100)² = 50000 - 10000 = ${R2(100)}`,
        },
        {
          question: '4. Revenue from 200 units of sister product',
          answer: formatCurrency(R2(200)),
          calculation: `R₂(200) = 500(200) - (200)² = 100000 - 40000 = ${R2(200)}`,
        },
        {
          question: '5. Effect of price on revenue',
          answer: 'Revenue decreases as quantity increases due to price reduction',
          calculation: `At 200 units: ${formatCurrency(R2(200))} < At 100 units: ${formatCurrency(R2(100))}`,
        },
      ],
    }
  })

  // Task 4: Efficiency Analysis
  const task4 = computed<Task>(() => {
    const { AP } = functions.value
    const A = analystId.value

    return {
      title: 'Task 4: Efficiency Analysis (Division)',
      description: 'Calculating average profit per unit to measure efficiency.',
      results: [
        {
          question: '1. Average profit function AP(x)',
          answer: `AP(x) = -5x + ${900 - 2 * A} - ${50 * A}/x`,
          calculation: `AP(x) = P(x)/x = (-5x² + ${900 - 2 * A}x - ${50 * A})/x = -5x + ${900 - 2 * A} - ${50 * A}/x`,
        },
        {
          question: '2. Average profit per unit when 20 units sold',
          answer: formatCurrency(AP(20)),
          calculation: `AP(20) = -5(20) + ${900 - 2 * A} - ${50 * A}/20 = ${AP(20)}`,
        },
        {
          question: '3. Average profit per unit when 50 units sold',
          answer: formatCurrency(AP(50)),
          calculation: `AP(50) = -5(50) + ${900 - 2 * A} - ${50 * A}/50 = ${AP(50)}`,
        },
        {
          question: '4. Average profit per unit when 100 units sold',
          answer: formatCurrency(AP(100)),
          calculation: `AP(100) = -5(100) + ${900 - 2 * A} - ${50 * A}/100 = ${AP(100)}`,
        },
        {
          question: '5. Limit as x approaches infinity',
          answer: 'Approaches -∞ (negative infinity)',
          calculation: 'As x → ∞, the -5x term dominates, so AP(x) → -∞',
        },
      ],
    }
  })

  // Task 5: Net Bonus Analysis
  const task5 = computed<Task>(() => {
    const { B, T, N, R } = functions.value

    return {
      title: 'Task 5: Net Bonus Analysis (Composition)',
      description: 'Analyzing employee bonuses through function composition.',
      results: [
        {
          question: '1. Net Bonus function N(x) = T(B(R(x)))',
          answer: 'N(x) = 0.85(0.10(-3x² + 1000x) - 500) = -0.255x² + 85x - 425',
          calculation:
            'N(x) = T(B(R(x))) = 0.85(0.10(-3x² + 1000x) - 500) = 0.85(-0.3x² + 100x - 500) = -0.255x² + 85x - 425',
        },
        {
          question: '2. Net bonus if weekly revenue is ₱20,000',
          answer: (() => {
            // Find x where R(x) = 20000: -3x² + 1000x = 20000
            // 3x² - 1000x + 20000 = 0
            const a = 3,
              b = -1000,
              c = 20000
            const discriminant = b * b - 4 * a * c
            const x1 = (-b + Math.sqrt(discriminant)) / (2 * a)
            const x2 = (-b - Math.sqrt(discriminant)) / (2 * a)
            const x = Math.min(x1, x2) // Take the smaller positive root
            return formatCurrency(N(x))
          })(),
          calculation: 'Solve -3x² + 1000x = 20000, then calculate N(x)',
        },
        {
          question: '3. Net bonus if weekly revenue is ₱50,000',
          answer: (() => {
            // Find x where R(x) = 50000
            const a = 3,
              b = -1000,
              c = 50000
            const discriminant = b * b - 4 * a * c
            const x1 = (-b + Math.sqrt(discriminant)) / (2 * a)
            const x2 = (-b - Math.sqrt(discriminant)) / (2 * a)
            const x = Math.min(x1, x2)
            return formatCurrency(N(x))
          })(),
          calculation: 'Solve -3x² + 1000x = 50000, then calculate N(x)',
        },
        {
          question: '4. Intermediate function B(R(x))',
          answer: 'B(R(x)) = 0.10(-3x² + 1000x) - 500 = -0.3x² + 100x - 500',
          calculation: 'This represents the gross bonus before tax withholding',
        },
        {
          question: '5. New composite with T₂(b) = b - 1000',
          answer: 'N₂(x) = (-0.3x² + 100x - 500) - 1000 = -0.3x² + 100x - 1500',
          calculation: 'N₂(x) = T₂(B(R(x))) = B(R(x)) - 1000',
        },
      ],
    }
  })

  return {
    analystId,
    functions,
    formatCurrency,
    task1,
    task2,
    task3,
    task4,
    task5,
  }
}
