<template>
  <UCard>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ task.title }}
        </h2>
        <UBadge 
          :label="taskNumber" 
          color="primary" 
          variant="solid"
        />
      </div>
      <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">
        {{ task.description }}
      </p>
    </template>

    <div class="space-y-6">
      <div 
        v-for="(result, index) in task.results" 
        :key="index"
        class="border-l-4 border-blue-200 dark:border-blue-800 pl-4"
      >
        <!-- Question -->
        <h3 class="font-medium text-gray-900 dark:text-white mb-2">
          {{ result.question }}
        </h3>

        <!-- Answer -->
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-3">
          <div class="flex items-start gap-2">
            <UIcon 
              name="i-heroicons-check-circle" 
              class="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" 
            />
            <div>
              <p class="font-medium text-green-800 dark:text-green-200">
                Answer:
              </p>
              <p class="text-green-700 dark:text-green-300 font-mono text-sm mt-1">
                {{ result.answer }}
              </p>
            </div>
          </div>
        </div>

        <!-- Calculation (if provided) -->
        <div 
          v-if="result.calculation" 
          class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"
        >
          <div class="flex items-start gap-2">
            <UIcon 
              name="i-heroicons-calculator" 
              class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" 
            />
            <div>
              <p class="font-medium text-blue-800 dark:text-blue-200">
                Calculation:
              </p>
              <p class="text-blue-700 dark:text-blue-300 font-mono text-sm mt-1 whitespace-pre-wrap">
                {{ result.calculation }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { Task } from '~/types/business'

interface Props {
  task: Task
}

const props = defineProps<Props>()

// Extract task number from title
const taskNumber = computed(() => {
  const match = props.task.title.match(/Task (\d+)/)
  return match ? `Task ${match[1]}` : 'Task'
})
</script>
