// Quick test of the point range utility functions
import { parsePointRange, getPointsInRange, isValidPointForRange } from './app/types/grading.js'

console.log('Testing point range utilities...')

// Test parsePointRange
console.log('\n1. Testing parsePointRange:')
console.log('parsePointRange("10-9"):', parsePointRange("10-9"))
console.log('parsePointRange("8-7"):', parsePointRange("8-7"))
console.log('parsePointRange("2-0"):', parsePointRange("2-0"))

// Test getPointsInRange
console.log('\n2. Testing getPointsInRange:')
console.log('getPointsInRange("10-9"):', getPointsInRange("10-9"))
console.log('getPointsInRange("8-7"):', getPointsInRange("8-7"))
console.log('getPointsInRange("6-5"):', getPointsInRange("6-5"))
console.log('getPointsInRange("2-0"):', getPointsInRange("2-0"))

// Test isValidPointForRange
console.log('\n3. Testing isValidPointForRange:')
console.log('isValidPointForRange(10, "10-9"):', isValidPointForRange(10, "10-9"))
console.log('isValidPointForRange(9, "10-9"):', isValidPointForRange(9, "10-9"))
console.log('isValidPointForRange(8, "10-9"):', isValidPointForRange(8, "10-9"))
console.log('isValidPointForRange(7, "8-7"):', isValidPointForRange(7, "8-7"))
console.log('isValidPointForRange(1, "2-0"):', isValidPointForRange(1, "2-0"))

console.log('\nAll tests completed!')
